#!/usr/bin/env python3
"""
Test script to demonstrate the new quality data extraction functionality
"""

import pandas as pd
import os
import sys

# Add current directory to path to import the modified module
sys.path.append('.')

def create_sample_csv():
    """Create a sample result.csv file for testing"""
    sample_data = [
        ['2024-01-15', 'test_branch', 'abc123', 'config_branch', 'def456', 'test.cfg', '1Pass', True, 
         'h264', 'comic_ElephantsDream_1280x720p24', -2.5, -1.8, -3.2],
        ['2024-01-15', 'test_branch', 'abc123', 'config_branch', 'def456', 'test.cfg', '1Pass', True, 
         'h264', 'game_Gujian_1280x720p60', -2.1, -1.5, -2.8],
        ['2024-01-15', 'test_branch', 'abc123', 'config_branch', 'def456', 'test.cfg', '1Pass', True, 
         'h265', 'comic_ElephantsDream_1280x720p24', -1.8, -1.2, -2.1],
        ['2024-01-15', 'test_branch', 'abc123', 'config_branch', 'def456', 'test.cfg', '1Pass', True, 
         'h265', 'game_Gujian_1280x720p60', -1.5, -1.0, -1.9],
        ['2024-01-15', 'test_branch', 'abc123', 'config_branch', 'def456', 'test.cfg', '1Pass', True, 
         'av1', 'comic_ElephantsDream_1280x720p24', -1.2, -0.8, -1.5],
        ['2024-01-15', 'test_branch', 'abc123', 'config_branch', 'def456', 'test.cfg', '1Pass', True, 
         'av1', 'game_Gujian_1280x720p60', -1.0, -0.6, -1.2]
    ]
    
    columns = ['DATE', 'videoip_branch', 'Videoip_hash', 'cfg_branch', 'cfg_files_hash', 
               'cfg_file_name', 'test_type', 'withTAV', 'CODEC', 'SEQUENCE', 
               'BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']
    
    df = pd.DataFrame(sample_data, columns=columns)
    df.to_csv('result.csv', index=False)
    print("Created sample result.csv file")

def create_sample_excel():
    """Create a sample Excel file for testing"""
    sample_ref_data = [
        ['h264', 'comic_ElephantsDream_1280x720p24', -2.3, -1.6, -3.0],
        ['h264', 'game_Gujian_1280x720p60', -2.0, -1.4, -2.7],
        ['h265', 'comic_ElephantsDream_1280x720p24', -1.7, -1.1, -2.0],
        ['h265', 'game_Gujian_1280x720p60', -1.4, -0.9, -1.8],
        ['av1', 'comic_ElephantsDream_1280x720p24', -1.1, -0.7, -1.4],
        ['av1', 'game_Gujian_1280x720p60', -0.9, -0.5, -1.1]
    ]
    
    columns = ['CODEC', 'SEQUENCE', 'BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']
    df = pd.DataFrame(sample_ref_data, columns=columns)
    
    # Create Excel file with the expected naming pattern
    excel_filename = 'cnm_wave677_test_self.xlsx'
    df.to_excel(excel_filename, sheet_name='Summary', index=False)
    print(f"Created sample Excel file: {excel_filename}")

def test_extraction():
    """Test the quality data extraction function"""
    print("Testing quality data extraction...")
    
    # Import the function from the modified file
    try:
        from top_level_videoquality import extract_and_display_quality_data, setup_logging
        
        # Setup logging
        logger = setup_logging()
        
        # Run the extraction
        result = extract_and_display_quality_data()
        
        if result:
            print("\n✅ Quality data extraction completed successfully!")
        else:
            print("\n❌ Quality data extraction failed!")
            
    except ImportError as e:
        print(f"❌ Could not import function: {e}")
    except Exception as e:
        print(f"❌ Error during extraction: {e}")

def main():
    """Main test function"""
    print("=" * 60)
    print("QUALITY DATA EXTRACTION TEST")
    print("=" * 60)
    
    # Create sample data files
    create_sample_csv()
    create_sample_excel()
    
    print("\n" + "-" * 40)
    print("Running extraction test...")
    print("-" * 40)
    
    # Test the extraction
    test_extraction()
    
    # Cleanup
    print("\n" + "-" * 40)
    print("Cleaning up test files...")
    print("-" * 40)
    
    test_files = ['result.csv', 'cnm_wave677_test_self.xlsx', 'jenkins_log.txt', 'scriptlog.txt']
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"Removed: {file}")

if __name__ == "__main__":
    main()
