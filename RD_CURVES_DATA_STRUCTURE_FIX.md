# RD Curves Data Structure Fix

## Problem Identified
The RD curve functions were still empty because of a **data structure mismatch**. The functions were looking for data in the wrong location within the `simulation_graphs` dictionary.

## Root Cause Analysis

### **Data Storage Pattern (from gaudi_quality.py):**
```python
# Line 278: Initialize structure
simulation_graph[main_group][group] = {}

# Line 295: Get reference to main_group level
simulation_g = simulation_graph[main_group]

# Line 300: Store raw data
gaudi_quality_lib.add_entry_raw_data(simulation_g, group, sim_name, input_data, reference_data)
```

### **Actual Data Structure:**
```
simulation_graph[main_group][ref_type][sim_name] = {
    'br_seq': [...],
    'psnr_y_seq': [...],
    'vmaf_seq': [...],
    'ssim_y_seq': [...],
    ...
}
```

Where:
- `main_group` = encoder name (e.g., 'cnm_wave677')
- `ref_type` = codec type (e.g., 'h264', 'h265', 'av1') 
- `sim_name` = full sequence key (e.g., 'h264_comic_ElephantsDream_1280x720p24')

### **Original Wrong Access Pattern:**
```python
# RD curve functions were trying to access:
simulation_graphs[main_group][seq_key]  # WRONG - missing ref_type level
```

### **Correct Access Pattern:**
```python
# Should access:
simulation_graphs[main_group][ref_type][seq_key]  # CORRECT
```

## Fixes Applied

### **1. Enhanced Debug Output in `add_entry_raw_data`:**
```python
def add_entry_raw_data(simulation_graphs, main_group, sim_name, data, ref):
    print(f"Debug: Storing RAW data with key '{sim_name}' in group '{main_group}'")
    # This will show exactly what keys are being stored
```

### **2. Fixed Data Structure Access in All RD Curve Functions:**

**Before:**
```python
if main_group not in simulation_graphs:
    return row_index
    
if seq_key in simulation_graphs[main_group]:  # WRONG
    br_seq = simulation_graphs[main_group][seq_key]['br_seq']  # WRONG
```

**After:**
```python
if main_group not in simulation_graphs:
    return row_index
    
if ref_type not in simulation_graphs[main_group]:  # NEW CHECK
    return row_index
    
if seq_key in simulation_graphs[main_group][ref_type]:  # CORRECT
    br_seq = simulation_graphs[main_group][ref_type][seq_key]['br_seq']  # CORRECT
```

### **3. Enhanced Debug Output in RD Curve Functions:**
```python
print(f"Debug: Found {len(simulation_graphs[main_group][ref_type])} sequences in simulation_graphs[{main_group}][{ref_type}]")
for key in list(simulation_graphs[main_group][ref_type].keys())[:3]:
    print(f"  - {key}")
```

### **4. Improved Key Matching Logic:**
```python
# Based on gaudi_quality.py line 300: sim_name = group + '_' + sequence
possible_keys = [
    ref_type + '_' + seq_processed,      # e.g., "h264_comic_ElephantsDream_1280x720p24"
    ref_type + '_' + seq,                # e.g., "h264_comic_ElephantsDream_1280x720p24_8bit"
    seq_processed,                       # e.g., "comic_ElephantsDream_1280x720p24"
    seq                                  # e.g., "comic_ElephantsDream_1280x720p24_8bit"
]
```

## Expected Debug Output

### **During Data Storage:**
```
Debug: Storing RAW data with key 'h264_comic_ElephantsDream_1280x720p24' in group 'h264'
Debug: Storing RAW data with key 'h265_comic_ElephantsDream_1280x720p24' in group 'h265'
Debug: Storing RAW data with key 'av1_comic_ElephantsDream_1280x720p24' in group 'av1'
```

### **During RD Curve Generation:**
```
start PSNR RD Curve RD Curve H264 PSNR
Debug: Found 5 sequences in simulation_graphs[cnm_wave677][h264]
  - h264_comic_ElephantsDream_1280x720p24
  - h264_sequence2_name
  - h264_sequence3_name
Debug: Found raw data for sequence 'comic_ElephantsDream_1280x720p24' with key 'h264_comic_ElephantsDream_1280x720p24'
```

## Key Insights

### **1. Data Storage Hierarchy:**
```
simulation_graphs
├── main_group (encoder)
│   ├── ref_type (codec)
│   │   ├── sim_name (sequence)
│   │   │   ├── br_seq: [array]
│   │   │   ├── psnr_y_seq: [array]
│   │   │   ├── vmaf_seq: [array]
│   │   │   └── ...
```

### **2. Key Format:**
- **Stored as:** `ref_type + '_' + sequence_name`
- **Example:** `'h264_comic_ElephantsDream_1280x720p24'`

### **3. Access Pattern:**
- **Full path:** `simulation_graphs[main_group][ref_type][sim_name]`
- **Example:** `simulation_graphs['cnm_wave677']['h264']['h264_comic_ElephantsDream_1280x720p24']`

## Expected Results

### **Before Fix:**
```
Warning: No SSIM raw data found for sequence 'comic_ElephantsDream_1280x720p24' in simulation_graphs
```

### **After Fix:**
```
Debug: Found 5 sequences in simulation_graphs[cnm_wave677][h264]
  - h264_comic_ElephantsDream_1280x720p24
  - h264_other_sequence_name
Debug: Found SSIM raw data for sequence 'comic_ElephantsDream_1280x720p24' with key 'h264_comic_ElephantsDream_1280x720p24'
```

## Files Modified
- **`gaudi_quality_lib.py`** - Fixed data structure access in all RD curve functions and enhanced debug output

## Next Steps
1. **Run your scripts** and check for the new debug output
2. **Verify RD curve sheets** now contain charts and data
3. **Look for the debug messages** showing successful data loading
4. **Remove debug print statements** once everything works correctly

The RD curves should now successfully find and use the raw measurement data to generate proper rate-distortion charts!
