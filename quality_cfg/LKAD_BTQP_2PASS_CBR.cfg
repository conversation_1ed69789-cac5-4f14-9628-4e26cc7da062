[Basic Parameters]
 codec_output                          = 0,1,2                                   ; valid entry= 0(avc),1(hevc),2(av1)
 simulation_type                       = 0                                       ; valid entry= 0(cbr),1(cqp)
 mode                                  = 0                                       ; valid entry= 0(bframes),1(ippp)

[codec]
 cnm_wave677                           = 1                                       ; valid entry= 0,1

[Xcoder Parameters]
ChromaFormat = 1
RcInitLevel = 8
CULevelRateControl = 0
MultiRef  = 3
cme_mvp_center = 1
MEImprove = 15
CMERefine = 1
FMEImprove = 1
BitEstEnable = 1
Trim1s = 1
RDOLevel = 1
DpbOptEn = 1
EnLookahead = 3
BtqpPropagateMode = 2
BtqpAdaptiveMode = 0
BtqpStrRatio = 32
StoreOrg = 1
QP = 10
EnAvgDqp = 1
SatdCtrl = 1
LKADPass = 1
Pass = 1
TwoPassLookaheadLength = 16
LkadLength = 16

[Xcoder Parameters:h265]
max_tu_depth_inter = 2
sign_hide_flag = 1
RdoqEnable = 1
RdoqEnOffset = 1

[Xcoder Parameters:h264]
RdoqEnable = 5
RdoqEnOffset = 0

[Xcoder Parameters:av1]
sign_hide_flag = 1
RdoqEnable = 1
RdoqEnOffset = 1
CflCtrl = 1
max_tu_depth_inter = 2
MaxTuDepthIntra = 2
AV1_EXT_MODES =  1
Av1CompPred = 1
Av1LocalWarp = 1

[Input Video Sequence]
 comic_ElephantsDream_1280x720p24_200             = 1                                       ; valid entry= 0,1
 game_Gujian_1280x720p60_200                      = 1                                       ; valid entry= 0,1
 mtv_ChineseSpringFestivalGala_1280x720p25_200    = 1                                       ; valid entry= 0,1
 scenery_TaiPei_1280x720p30_200                   = 1                                       ; valid entry= 0,1

[Bitrate Sequence]
 1000000                               = 1                                       ; valid entry= 0,1
 2000000                               = 1                                       ; valid entry= 0,1
 3000000                               = 1                                       ; valid entry= 0,1
 4000000                               = 1                                       ; valid entry= 0,1
 5000000                               = 1                                       ; valid entry= 0,1
 6000000                               = 1                                       ; valid entry= 0,1

[Anchor Sequence]
 BTQP_2PASS                                = 1                                       ; valid entry= 0,1

