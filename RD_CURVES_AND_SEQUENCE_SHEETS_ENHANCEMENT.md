# RD Curves and Sequence Sheets Enhancement

## Overview
Enhanced the Excel generation to include additional RD curve sheets for VMAF and SSIM metrics, and added sequence sheets for low, medium, and high bitrate ranges.

## New Features Added

### 1. **Additional RD Curve Sheets**

#### **Before:**
- RD Curve H264 (PSNR only)
- RD Curve H265 (PSNR only)  
- RD Curve AV1 (PSNR only)

#### **After:**
- **RD Curve H264 PSNR** - Rate-Distortion curves using PSNR metric
- **RD Curve H264 VMAF** - Rate-Distortion curves using VMAF metric
- **RD Curve H264 SSIM** - Rate-Distortion curves using SSIM metric
- **RD Curve H265 PSNR** - Rate-Distortion curves using PSNR metric
- **RD Curve H265 VMAF** - Rate-Distortion curves using VMAF metric
- **RD Curve H265 SSIM** - Rate-Distortion curves using SSIM metric
- **RD Curve AV1 PSNR** - Rate-Distortion curves using PSNR metric
- **RD Curve AV1 VMAF** - Rate-Distortion curves using VMAF metric
- **RD Curve AV1 SSIM** - Rate-Distortion curves using SSIM metric

### 2. **Additional Sequence Sheets by Range**

#### **Before:**
- Sequences H.264 (all data only)
- Sequences H.265 (all data only)
- Sequences AV1 (all data only)

#### **After:**
- **Sequences H.264 All** - BD-rate data using all bitrate points
- **Sequences H.264 Low** - BD-rate data using low bitrate range
- **Sequences H.264 Medium** - BD-rate data using medium bitrate range
- **Sequences H.264 High** - BD-rate data using high bitrate range
- **Sequences H.265 All** - BD-rate data using all bitrate points
- **Sequences H.265 Low** - BD-rate data using low bitrate range
- **Sequences H.265 Medium** - BD-rate data using medium bitrate range
- **Sequences H.265 High** - BD-rate data using high bitrate range
- **Sequences AV1 All** - BD-rate data using all bitrate points
- **Sequences AV1 Low** - BD-rate data using low bitrate range
- **Sequences AV1 Medium** - BD-rate data using medium bitrate range
- **Sequences AV1 High** - BD-rate data using high bitrate range

## Technical Implementation

### **New Functions Added:**

1. **`generate_graph_worksheet_vmaf()`**
   - Creates RD curves using VMAF scores vs bitrate
   - Uses columns 10 (reference) and 18 (encoder) for VMAF data
   - Y-axis label: "VMAF Score"

2. **`generate_graph_worksheet_ssim()`**
   - Creates RD curves using SSIM scores vs bitrate
   - Uses columns 7 (reference) and 15 (encoder) for SSIM Y data
   - Y-axis label: "SSIM Score"

3. **`generate_summary_worksheet_per_sequence_range()`**
   - Generates sequence worksheets for specific ranges (low, medium, high, all)
   - Filters data based on suffix: `_low`, `_medium`, `_high`, `_all`
   - Provides range-specific headers and averages

### **Enhanced Logic:**

1. **Automatic Range Detection:**
   ```python
   # Determine the range suffix from worksheet name
   range_suffix = 'all'  # default
   if 'Low' in worksheet.name:
       range_suffix = 'low'
   elif 'Medium' in worksheet.name:
       range_suffix = 'medium'
   elif 'High' in worksheet.name:
       range_suffix = 'high'
   ```

2. **Metric Type Detection:**
   ```python
   # Determine metric type from worksheet name
   metric_type = 'PSNR'  # default
   if 'VMAF' in worksheet.name:
       metric_type = 'VMAF'
   elif 'SSIM' in worksheet.name:
       metric_type = 'SSIM'
   ```

3. **Data Filtering by Range:**
   ```python
   # Try multiple possible key formats with specific range suffix
   possible_keys = [
       ref_type + '_' + seq_processed + '_' + range_suffix,  # e.g., "h264_BasketballDrill_832x480p50_low"
       ref_type + '_' + seq + '_' + range_suffix,            # e.g., "h264_BasketballDrill_832x480p50_8bit_low"
       seq_processed + '_' + range_suffix,                   # Just the sequence name with range
       seq + '_' + range_suffix                              # Original sequence name with range
   ]
   ```

## Data Sources

### **RD Curves Use:**
- **Raw measurement arrays** from `simulation_graphs`
- **Point-by-point data** for each QP/CRF setting
- **Multiple metrics:** PSNR, VMAF, SSIM
- **Visual representation** of rate-distortion trade-offs

### **Sequence Sheets Use:**
- **BD-rate values** from `simulation_results`
- **Range-specific data** with suffixes: `_low`, `_medium`, `_high`, `_all`
- **Aggregated results** showing performance differences
- **Summary statistics** with proper averages

## Benefits

### **1. Comprehensive Analysis:**
- **Multiple Quality Metrics:** Compare encoder performance using PSNR, VMAF, and SSIM
- **Range-Specific Analysis:** Understand performance at different bitrate ranges
- **Visual Comparison:** RD curves show performance trends clearly

### **2. Better Decision Making:**
- **VMAF Analysis:** Industry-standard perceptual quality metric
- **SSIM Analysis:** Structural similarity assessment
- **Range Analysis:** Optimize for specific use cases (low/medium/high bitrate)

### **3. Detailed Reporting:**
- **Separate sheets** for each metric and range combination
- **Clear labeling** with metric type and range in headers
- **Proper averages** calculated only from valid data

## Usage

### **When to Use Each Sheet:**

1. **RD Curve PSNR:** Traditional quality analysis, technical evaluation
2. **RD Curve VMAF:** Perceptual quality analysis, user experience focus
3. **RD Curve SSIM:** Structural quality analysis, detail preservation
4. **Sequences All:** Overall performance across all bitrates
5. **Sequences Low:** Low bitrate applications (mobile, streaming)
6. **Sequences Medium:** Standard applications (broadcast, web)
7. **Sequences High:** High quality applications (archival, professional)

## Debug Output

The enhanced functions provide detailed debug output:
- Shows which range suffix is being processed
- Lists available sequences for each range
- Indicates successful data loading
- Warns about missing data

## Files Modified

- **`gaudi_quality_lib.py`** - Added new functions and enhanced worksheet processing logic

## Expected Results

After running your scripts, you should see:
- **9 RD Curve sheets** (3 codecs × 3 metrics)
- **12 Sequence sheets** (3 codecs × 4 ranges)
- **Enhanced charts** with appropriate Y-axis labels for each metric
- **Range-specific data** in sequence sheets
- **Debug output** showing processing details
