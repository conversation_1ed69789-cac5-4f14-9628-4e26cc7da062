# Configuration Setup for Video Quality Script

## JSON Configuration File

The script now supports loading credentials from a JSON configuration file instead of hardcoded values.

### Setup Instructions

1. **Copy the example config file:**
   ```bash
   cp config.json.example config.json
   ```

2. **Edit the config.json file with your credentials:**
   ```json
   {
       "host_server": "YVR1LabSlurm04",
       "user": "your_actual_username",
       "password": "your_actual_password",
       "description": "SSH credentials for video quality testing server"
   }
   ```

3. **Secure the config file (recommended):**
   ```bash
   chmod 600 config.json
   ```

### Configuration Options

| Field | Description | Default |
|-------|-------------|---------|
| `host_server` | SSH server hostname or IP | `YVR1LabSlurm04` |
| `user` | SSH username | `nvme` |
| `password` | SSH password | `logan` |
| `description` | Optional description field | - |

### Security Notes

- The `config.json` file should be kept secure and not committed to version control
- Add `config.json` to your `.gitignore` file
- Use appropriate file permissions (600) to restrict access
- Consider using SSH keys instead of passwords for better security

### Fallback Behavior

If `config.json` is not found or cannot be read:
- The script will use default values
- A warning will be logged
- The script will continue execution

### Jenkins Integration

For Jenkins:
1. Store the `config.json` file in the workspace or a secure location
2. Ensure the Jenkins user has read access to the file
3. The script will automatically detect and use the configuration

### Example Jenkins Pipeline

```groovy
pipeline {
    agent any
    stages {
        stage('Setup Config') {
            steps {
                // Copy config from secure location
                sh 'cp /secure/path/config.json .'
            }
        }
        stage('Run Quality Test') {
            steps {
                sh 'python3 top_level_videoquality.py -j ${BUILD_NUMBER} --save_to_db'
            }
        }
    }
    post {
        always {
            // Clean up config file
            sh 'rm -f config.json'
        }
    }
}
```

### Troubleshooting

**Config file not found:**
- Check if `config.json` exists in the script directory
- Verify file permissions
- Check the log output for specific error messages

**Invalid JSON:**
- Validate JSON syntax using `python -m json.tool config.json`
- Ensure all required fields are present
- Check for trailing commas or syntax errors

**Connection issues:**
- Verify credentials are correct
- Test SSH connection manually: `ssh user@host`
- Check network connectivity and firewall settings
