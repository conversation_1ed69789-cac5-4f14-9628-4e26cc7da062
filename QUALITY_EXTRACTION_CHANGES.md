# Quality Data Extraction Changes

## Overview
Modified `top_level_videoquality.py` to extract and display quality metrics (PSNR, VMAF, SSIM) from Excel sheets and CSV files instead of comparing with database values.

## Key Changes Made

### 1. Replaced `compare_bdrate()` Function
- **Old Function**: `compare_bdrate(with_TAV, video_ip_branch, config_filename)`
  - Fetched reference data from MySQL database
  - Compared test results with database values
  - Calculated differences and checked thresholds
  - Failed build if quality differences exceeded 1%

- **New Function**: `extract_and_display_quality_data()`
  - Extracts test data from `result.csv` file
  - Optionally reads reference data from Excel files (if available)
  - Displays quality metrics in a formatted table
  - No database dependency
  - No threshold checking or build failures

### 2. Updated Function Call
```python
# Old code:
quality_ok = compare_bdrate(withTAV, args.Compare_with, args.config_file)
if not quality_ok:
    logger.error("Quality threshold exceeded - failing build")
    sys.exit(ExitCodes.QUALITY_THRESHOLD_EXCEEDED.value)

# New code:
quality_ok = extract_and_display_quality_data()
if not quality_ok:
    logger.error("Failed to extract quality data")
    sys.exit(ExitCodes.FILE_OPERATION_FAILED.value)
```

### 3. Added Excel Reading Support
- Added imports for `openpyxl` and `xlrd` libraries
- Graceful fallback if Excel libraries are not available

## What the New Function Does

### Data Sources
1. **Primary Source**: `result.csv` - Contains test results with BDRATE_PSNR, BDRATE_SSIM, BDRATE_VMAF values
2. **Optional Reference**: Excel files matching pattern `cnm_wave677_*.xlsx` - Contains reference values

### Data Processing
1. **Groups CSV data** by CODEC and SEQUENCE, taking minimum BD-rate values
2. **Searches for Excel files** in current directory with expected naming pattern
3. **Attempts to read Excel data** from common sheet names (Summary, Results, Data, Sheet1)
4. **Matches data** between CSV and Excel based on codec and sequence

### Output Format
The function displays two tables:

#### 1. Detailed Results Table
```
CODEC  SEQUENCE                              TEST_PSNR  TEST_SSIM  TEST_VMAF  REF_PSNR  REF_SSIM  REF_VMAF
h264   comic_ElephantsDream_1280x720p24     -2.50      -1.80      -3.20      -2.30     -1.60     -3.00
h264   game_Gujian_1280x720p60              -2.10      -1.50      -2.80      -2.00     -1.40     -2.70
...
```

#### 2. Average Values by Codec
```
CODEC  TEST_PSNR  TEST_SSIM  TEST_VMAF  REF_PSNR  REF_SSIM  REF_VMAF
h264   -2.30      -1.65      -3.00      -2.15     -1.50     -2.85
h265   -1.65      -1.10      -2.00      -1.55     -1.00     -1.90
...
```

## Benefits of the Changes

### 1. Simplified Operation
- No database dependency
- No complex comparison logic
- No threshold-based build failures
- Pure data extraction and display

### 2. Flexible Data Sources
- Works with CSV files (always)
- Optionally uses Excel files for reference data
- Graceful handling when Excel files are missing

### 3. Clear Output
- Well-formatted tables showing all quality metrics
- Separate display of detailed and average values
- Clear logging of what data was found and processed

### 4. Robust Error Handling
- Continues operation even if Excel files can't be read
- Provides informative error messages
- Returns success/failure status

## Testing

A test script `test_quality_extraction.py` is provided that:
1. Creates sample CSV and Excel files
2. Tests the extraction function
3. Displays the output
4. Cleans up test files

To run the test:
```bash
python test_quality_extraction.py
```

## Usage

The modified script works the same way as before, but now:
1. **Extracts** quality data instead of comparing with database
2. **Displays** results in formatted tables
3. **Does not fail** builds based on quality thresholds
4. **Provides** clear visibility into test results

## Dependencies

The script now optionally requires Excel reading libraries:
- `openpyxl` (preferred) or `xlrd` for reading Excel files
- `pandas` for data manipulation (already required)

If Excel libraries are not available, the script will still work but only display CSV data without reference values.
