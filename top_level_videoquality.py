import time, sys, argparse, os, re, select, csv, datetime, logging
import paramiko
from subprocess import getoutput
import json
from mysqlLib import mySQL
import pandas as pd
from enum import Enum
from typing import Optional, Tuple, Dict, Any, Union

# Exit codes for Jenkins integration
class ExitCodes(Enum):
    SUCCESS = 0
    CONFIG_VALIDATION_FAILED = 10
    SSH_CONNECTION_FAILED = 11
    JOB_EXECUTION_FAILED = 12
    FILE_OPERATION_FAILED = 13
    DATABASE_ERROR = 14
    QUALITY_THRESHOLD_EXCEEDED = 15
    GENERAL_ERROR = 1

# Configure logging for <PERSON>
def setup_logging():
    """Setup logging configuration for Jenkins integration"""
    log_format = '%(asctime)s [%(levelname)s] %(message)s'
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('jenkins_log.txt', mode='w')
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

parser = argparse.ArgumentParser(formatter_class=argparse.RawTextHelpFormatter)


parser.add_argument('-j',
                    '--job',
                    type=str,
                    default="job1",
                    help='Provide the job name\n\n')
parser.add_argument('-b',
                    '--branch',
                    type=str,
                    default="main",
                    help='Provide the scripts branch name\n\n')
parser.add_argument('-g',
                    '--gaudibranch',
                    type=str,
                    default="RANGER_CM133_v6.0",
                    help='Provide the Gaudi compile branch name\n\n')
parser.add_argument('-f',
                    '--configbranch',
                    type=str,
                    default="master",
                    help='Provide the config files branch name\n\n')
parser.add_argument('-e',
                    '--enable_defination',
                    type=str,
                    default="",
                    help='Provide a defination to enable, ex: add_definitions(-DNETINT_RANGER_RC_V1_TAV)\n\n')
parser.add_argument('-d',
                    '--disable_defination',
                    type=str,
                    default="",
                    help='Provide a defination to disable, ex: add_definitions(-DNETINT_RANGER_RC_V1_TAV)\n\n')
parser.add_argument("--twopass", action="store_true", help="Run two pass test")
parser.add_argument("--custom_Gaudi", action="store_true", help="Run two pass test")
parser.add_argument("--save_to_db", action="store_true", help="To save data to DB")
parser.add_argument('-c',
                    '--compile_flag',
                    type=str,
                    default="",
                    help='Add compile flag\n\n')
parser.add_argument('-c1',
					'--config1',
					type=str,
					default="NONE.cfg",
                    help='Pass1 Config parameter\n\n')
parser.add_argument('-c2',
					'--config2',
					type=str,
					default="NONE.cfg",
                    help='Pass2 Config parameter\n\n')
parser.add_argument('-c3',
					'--config3',
					type=str,
					default="NONE.cfg",
                    help='Pass3 Config parameter\n\n')
parser.add_argument('-c4',
					'--config4',
					type=str,
					default="NONE.cfg",
                    help='Pass4 Config parameter\n\n')
parser.add_argument('-c5',
					'--config5',
					type=str,
					default="NONE.cfg",
                    help='Pass5 Config parameter\n\n')
parser.add_argument('-v',
					'--Compare_with',
					type=str,
					default="RANGER_CM_PX4_156_v2.0.0",
                    help='Provide branch name to compare with\n\n')
parser.add_argument('-i',
					'--config_file',
					type=str,
					default="BQTC_12_2024.cfg",
                    help='Provide config file to compare with\n\n')
args = parser.parse_args()
compile_flag = args.compile_flag

def ensure_cfg_extension(config):
    if not config.endswith('.cfg'):
        return config + '.cfg'
    return config

config1 = ensure_cfg_extension(args.config1)
config2 = ensure_cfg_extension(args.config2)
config3 = ensure_cfg_extension(args.config3)
config4 = ensure_cfg_extension(args.config4)
config5 = ensure_cfg_extension(args.config5)

enable_defination = args.enable_defination
disable_defination = args.disable_defination

# Load credentials from JSON config file
def load_credentials():
    """Load credentials from JSON config file with fallback to defaults"""
    config_file = 'config.json'
    default_config = {
        'host_server': 'YVR1LabSlurm04',
        'user': 'nvme',
        'password': 'logan'
    }

    try:
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
            logger.info(f"Loaded credentials from {config_file}")
            return config.get('host_server', default_config['host_server']), \
                   config.get('user', default_config['user']), \
                   config.get('password', default_config['password'])
        else:
            logger.warning(f"Config file {config_file} not found, using defaults")
            logger.warning("Consider creating config.json with your credentials")
            return default_config['host_server'], default_config['user'], default_config['password']
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in {config_file}: {e}")
        logger.info("Using default credentials")
        return default_config['host_server'], default_config['user'], default_config['password']
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        logger.info("Using default credentials")
        return default_config['host_server'], default_config['user'], default_config['password']

host_server, user, password = load_credentials()

def print_now(text):
	"""Legacy print function - use logger instead for new code"""
	with open('scriptlog.txt','a') as outputFile:
		outputFile.write(time.strftime("%Y-%m-%d %H:%M:%S") + ' '*7  + str(text) + '\n')
	logger.info(text)
	sys.stdout.flush()

def log_progress(current: int, total: int, operation: str):
	"""Log progress for long-running operations"""
	percent = (current / total) * 100 if total > 0 else 0
	logger.info(f"[PROGRESS] {operation}: {current}/{total} ({percent:.1f}%)")

def log_jenkins_section(section_name: str):
	"""Log section headers for Jenkins readability"""
	logger.info(f"\n{'='*60}")
	logger.info(f"  {section_name.upper()}")
	logger.info(f"{'='*60}")

def count_passes():
    # Count the number of non-NONE pass values
    passes = 0
    for pass_value in [config1, config2, config3, config4, config5]:
        if pass_value != "NONE.cfg":
            passes += 1
    return passes

def connectHost(timeoutloop=5) -> Optional[paramiko.SSHClient]:
	"""Establish SSH connection with retry logic and better error handling"""
	ssh = paramiko.SSHClient()
	ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

	for i in range(timeoutloop):
		start_time = time.time()
		try:
			logger.info(f"Attempting SSH connection to {host_server} (attempt {i+1}/{timeoutloop})")
			ssh.connect(
				host_server,
				username=user,
				password=password,
				timeout=60,
				look_for_keys=False,
				allow_agent=False,
				banner_timeout=60
			)
			ssh.get_transport().set_keepalive(30)
			logger.info(f"SSH connection established successfully")
			return ssh
		except paramiko.AuthenticationException as e:
			logger.error(f"SSH Authentication failed: {e}")
			return None  # Don't retry on auth failures
		except paramiko.SSHException as e:
			logger.warning(f"SSH connection attempt {i+1} failed: {e}")
		except Exception as e:
			logger.warning(f"SSH connection attempt {i+1} failed with unexpected error: {e}")

		if i < timeoutloop - 1:  # Don't sleep on last attempt
			sleep_time = max(5, 60 - (time.time() - start_time))
			logger.info(f"Waiting {sleep_time:.1f}s before retry...")
			time.sleep(sleep_time)

	logger.error(f"SSH connection failed after {timeoutloop} attempts")
	return None

def exec_command(cmd: str, print_cmd: bool = True) -> Union[Optional[int], Tuple[Optional[str], Optional[int]]]:
	"""Execute command via SSH with improved error handling and logging"""
	ssh = connectHost()
	if ssh is None:
		logger.error("Cannot execute command: SSH connection failed")
		return None if print_cmd else (None, None)

	try:
		if print_cmd:
			logger.info(f"Executing: {cmd}")
			stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True)
			stdout.channel.setblocking(0)
			stderr.channel.setblocking(0)

			while not stdout.channel.exit_status_ready():
				# Use select to wait for data to be available
				rl, wl, xl = select.select([stdout.channel, stderr.channel], [], [], 1.0)
				if stdout.channel in rl:
					line = stdout.readline()
					if line:
						logger.info(f"[REMOTE] {line.strip()}")

				time.sleep(1)  # Prevent busy-waiting

			exit_status = stdout.channel.recv_exit_status()
			if exit_status != 0:
				logger.warning(f"Command exited with status {exit_status}")
			else:
				logger.info("Command completed successfully")
			return exit_status
		else:
			stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True)
			output = stdout.read().decode("utf-8")
			error_output = stderr.read().decode("utf-8")
			exit_status = stdout.channel.recv_exit_status()

			if exit_status != 0 and error_output:
				logger.warning(f"Command stderr: {error_output.strip()}")

			return output.strip(), exit_status

	except Exception as e:
		logger.error(f"Error executing command '{cmd}': {e}")
		return None if print_cmd else (None, None)
	finally:
		if ssh:
			ssh.close()

def disable_definition_by_line(cmake_file: str, line_to_disable: str) -> bool:
	"""Disable a definition line in CMakeLists.txt"""
	cmd = f"sed -i '/^{line_to_disable.strip()}/s/^/#/' {cmake_file}"
	output, exit_status = exec_command(cmd, print_cmd=False)
	if exit_status == 0:
		logger.info(f"Successfully disabled definition: {line_to_disable}")
		return True
	else:
		logger.error(f"Failed to disable definition: {line_to_disable}")
		return False

def enable_definition_by_line(cmake_file: str, line_to_enable: str) -> bool:
	"""Enable a definition line in CMakeLists.txt"""
	cmd = f"sed -i '/^#{line_to_enable.strip()}/s/^#//' {cmake_file}"
	output, exit_status = exec_command(cmd, print_cmd=False)
	if exit_status == 0:
		logger.info(f"Successfully enabled definition: {line_to_enable}")
		return True
	else:
		logger.error(f"Failed to enable definition: {line_to_enable}")
		return False
def enable_or_disable_definations(Video_ip_fw: str) -> bool:
	"""Enable or disable definitions in CMakeLists.txt"""
	cmake_file = f"{Video_ip_fw}/CMakeLists.txt"
	success = True

	if enable_defination:
		logger.info(f"Enabling definitions: {enable_defination}")
		enable_definations = enable_defination.split(',')
		for enable_def in enable_definations:
			if not enable_definition_by_line(cmake_file, enable_def.strip()):
				success = False

	if disable_defination:
		logger.info(f"Disabling definitions: {disable_defination}")
		disable_definations = disable_defination.split(',')
		for disable_def in disable_definations:
			if not disable_definition_by_line(cmake_file, disable_def.strip()):
				success = False

	return success


def upload_file(local_path: str, remote_path: str) -> bool:
	"""Upload file to remote server with error handling"""
	logger.info(f"Uploading {local_path} to {remote_path}")

	if not os.path.exists(local_path):
		logger.error(f"Local file does not exist: {local_path}")
		return False

	ssh = connectHost()
	if ssh is None:
		logger.error("Upload failed: SSH connection failed")
		return False

	try:
		sftp = ssh.open_sftp()
		file_size = os.path.getsize(local_path)
		logger.info(f"Uploading file ({file_size} bytes)...")

		sftp.put(local_path, remote_path)
		sftp.close()

		logger.info(f"File uploaded successfully: {local_path} -> {remote_path}")
		return True

	except Exception as e:
		logger.error(f"File upload failed: {e}")
		return False
	finally:
		if ssh:
			ssh.close()

def generate_csv(core_filename: str, metadata: Dict[str, Any]) -> bool:
	"""Generate CSV with metadata, with proper error handling"""
	try:
		if not os.path.exists(core_filename):
			logger.error(f"Core CSV file not found: {core_filename}")
			return False

		file_size = os.path.getsize(core_filename)
		if file_size == 0:
			logger.error(f"Core CSV file is empty: {core_filename}")
			return False

		logger.info(f"Processing CSV file: {core_filename} ({file_size} bytes)")

		with open(core_filename, mode='r', newline='') as core_file:
			reader = csv.reader(core_file)
			try:
				headers = next(reader)
			except StopIteration:
				logger.error(f"CSV file has no headers: {core_filename}")
				return False

			logger.info(f"CSV headers: {headers}")

			with open('result.csv', mode='w', newline='') as updated_file:
				writer = csv.writer(updated_file)
				writer.writerow(['DATE', 'videoip_branch', 'Videoip_hash','cfg_branch', 'cfg_files_hash', 'cfg_file_name','test_type', 'withTAV'] + headers)

				row_count = 0
				for row in reader:
					writer.writerow([
						metadata['DATE'], metadata['videoip_branch'], metadata['Videoip_hash'],
						metadata['cfg_branch'], metadata['cfg_files_hash'],
						metadata['cfg_file_name'], metadata['test_type'], metadata['withTAV']
					] + row)
					row_count += 1

				logger.info(f"Generated result.csv with {row_count} data rows")
				return True

	except Exception as e:
		logger.error(f"Failed to generate CSV: {e}")
		return False

def store_to_db(path_to_csv):
	db = mySQL()
	db.insertCSVData(path_to_csv, 'Results')

def download_file(remote_file_path, local_file_path):
	ssh = connectHost()
	if ssh:
		try:
			sftp = ssh.open_sftp()
			local_file_path = os.path.join(local_file_path, os.path.basename(remote_file_path))
			sftp.get(remote_file_path, local_file_path)
			print(f"Downloaded: {remote_file_path} to {local_file_path}")
			sftp.close()
		except Exception as e:
			print(f"Download failed: {str(e)}")
		finally:
			ssh.close()

def check_tav(config_file_path):
	withTAV = False
	try:
		cmd = f"cat {config_file_path}"
		output, exit_status = exec_command(cmd,print_cmd=False)
		if exit_status == 0:
			for line in output.splitlines():
				line = line.strip()
				if "AccurateRC" in line:
					key, value = line.split('=')
					key = key.strip()
					value = value.split(';')[0].strip()
					if key == "AccurateRC" and value == "1":
						withTAV = True
						break
		else:
			print(f"Failed to read the file: {config_file_path}")
	except FileNotFoundError:
		print(f"{config_file_path} not found.")
	return withTAV

def copy_files_to_job(job_dir: str, Video_ip_fw: str, num_passes: int) -> bool:
	"""Copy required files to job directory with error handling"""
	logger.info("Copying required files to job directory...")
	success = True

	# Copy files from regression_quick_quality_scripts to job
	quality_scripts_dir = f"{job_dir}/regression_quick_quality_scripts/*"
	simulations_dir = "/mnt/ceph/Automation/simulations/*"

	operations = [
		(f"cp -r {quality_scripts_dir} {job_dir}", "quality scripts"),
		(f"cp -r {simulations_dir} {job_dir}", "simulation files"),
		(f"cp -r {job_dir}/regression_cfg/quick_quality_cfg_files/* {job_dir}/quality_cfg/", "config files")
	]

	if not args.custom_Gaudi:
		operations.append((f"find {Video_ip_fw} -type f -name 'Gaudi*' -executable -exec cp {{}} {job_dir}/Gaudi \;", "Gaudi binary"))

	for cmd, description in operations:
		logger.info(f"Copying {description}...")
		exit_code = exec_command(cmd)
		if exit_code != 0:
			logger.error(f"Failed to copy {description}")
			success = False

	# Copy files for additional passes
	for i in range(1, num_passes):
		pass_dir = os.path.join(job_dir, f"pass{i}")
		logger.info(f"Setting up pass {i} directory...")

		pass_operations = [
			(f"mkdir -p {pass_dir}", f"pass{i} directory"),
			(f"cp -r {quality_scripts_dir} {pass_dir}", f"pass{i} quality scripts"),
			(f"cp -r {simulations_dir} {pass_dir}", f"pass{i} simulation files"),
			(f"cp -r {job_dir}/regression_cfg/quick_quality_cfg_files/* {pass_dir}/quality_cfg/", f"pass{i} config files")
		]

		if not args.custom_Gaudi:
			pass_operations.append((f"find {Video_ip_fw} -type f -name 'Gaudi*' -executable -exec cp {{}} {pass_dir}/Gaudi \;", f"pass{i} Gaudi binary"))

		for cmd, description in pass_operations:
			exit_code = exec_command(cmd, print_cmd=("mkdir" not in cmd))
			if exit_code != 0:
				logger.error(f"Failed to setup {description}")
				success = False

	if success:
		logger.info("All files copied successfully")
	else:
		logger.error("Some file copy operations failed")

	return success


def compare_bdrate(with_TAV,video_ip_branch,config_filename):
	pd.set_option('display.max_rows', None)   # Show all rows
	pd.set_option('display.max_columns', None)  # Show all columns
	pd.set_option('display.width', None)      # Don't wrap lines
	pd.set_option('display.max_colwidth', None)  # Show full content in each column (no truncation)

	withTAV = 1 if with_TAV else 0

	# Define the list of codecs and sequences
	codecs = ['av1', 'h265', 'h264']
	sequences = [
		'comic_ElephantsDream_1280x720p24',
		'game_Gujian_1280x720p60',
		'mtv_ChineseSpringFestivalGala_1280x720p25',
		'scenery_TaiPei_1280x720p30'
	]

	# Fetch data from the database
	query = f"""
	SELECT
		CODEC,
		SEQUENCE,
		MIN(BDRATE_PSNR) AS BDRATE_PSNR,
		MIN(BDRATE_SSIM) AS BDRATE_SSIM,
		MIN(BDRATE_VMAF) AS BDRATE_VMAF
	FROM
		Quality_Results.Results
	WHERE
		withTAV = {withTAV}
		AND videoip_branch = '{video_ip_branch}'
		AND cfg_file_name = '{config_filename}'
	GROUP BY
		CODEC,
		SEQUENCE;
	"""

	try:
		db = mySQL()
		db_data = db.sqlExecute(query)
	except Exception as e:
		print(f"Error fetching data from the database: {e}")
		db_data = pd.DataFrame()
	columns = ['CODEC', 'SEQUENCE', 'BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']
	db_data = pd.DataFrame(db_data, columns=columns)
	for col in ['BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']:
		db_data[col] = db_data[col].apply(float)

	# Read and filter data from the CSV file
	try:
		csv_data = pd.read_csv('result.csv')
	except Exception as e:
		print(f"Error reading CSV file: {e}")
		csv_data = pd.DataFrame()
	csv_data = csv_data.groupby(['CODEC', 'SEQUENCE']).agg({
		'BDRATE_PSNR': 'min',
		'BDRATE_SSIM': 'min',
		'BDRATE_VMAF': 'min'
	}).reset_index()
	comparison_results = []

	for codec in codecs:
		for sequence in sequences:
			db_row = db_data[(db_data['CODEC'] == codec) & (db_data['SEQUENCE'] == sequence)]
			csv_row = csv_data[(csv_data['CODEC'] == codec) & (csv_data['SEQUENCE'] == sequence)]
			if not db_row.empty and not csv_row.empty:
				# Extract
				db_values = db_row.iloc[0]
				csv_values = csv_row.iloc[0]
				# Compare and store results
				comparison_entry = {
					'CODEC': codec,
					'SEQUENCE': sequence
				}
				for metric in ['BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']:
					db_value = db_values[metric]
					csv_value = csv_values[metric].round(2)
					diff = round(db_value - csv_value, 2)
					comparison_entry[f'{metric}_db'] = db_value
					comparison_entry[f'{metric}_csv'] = csv_value
					comparison_entry[f'{metric}_diff'] = diff
				comparison_results.append(comparison_entry)
	comparison_df = pd.DataFrame(comparison_results)
	comparison_df = comparison_df.rename(columns={
		'BDRATE_PSNR_db': 'BD_PSNR_db',
		'BDRATE_PSNR_csv': 'BD_PSNR_csv',
		'BDRATE_PSNR_diff': 'BD_PSNR_diff',
		'BDRATE_SSIM_db': 'BD_SSIM_db',
		'BDRATE_SSIM_csv': 'BD_SSIM_csv',
		'BDRATE_SSIM_diff': 'BD_SSIM_diff',
		'BDRATE_VMAF_db': 'BD_VMAF_db',
		'BDRATE_VMAF_csv': 'BD_VMAF_csv',
		'BDRATE_VMAF_diff': 'BD_VMAF_diff'
	})
	print("\n")
	print(comparison_df.to_string(index=False))
	print("\n")
	average_df = comparison_df.groupby('CODEC').agg({
		'BD_PSNR_db': 'mean',
		'BD_PSNR_csv': 'mean',
		'BD_SSIM_db': 'mean',
		'BD_SSIM_csv': 'mean',
		'BD_VMAF_db': 'mean',
		'BD_VMAF_csv': 'mean'
	}).reset_index()

	# Calculate the differences between database and CSV averages
	average_df['BD_PSNR_diff'] = average_df['BD_PSNR_db'] - average_df['BD_PSNR_csv']
	average_df['BD_SSIM_diff'] = average_df['BD_SSIM_db'] - average_df['BD_SSIM_csv']
	average_df['BD_VMAF_diff'] = average_df['BD_VMAF_db'] - average_df['BD_VMAF_csv']

	# Rename the columns for clarity
	average_df = average_df.rename(columns={
		'BD_PSNR_db': 'Avg_BD_PSNR_db',
		'BD_PSNR_csv': 'Avg_BD_PSNR_csv',
		'BD_SSIM_db': 'Avg_BD_SSIM_db',
		'BD_SSIM_csv': 'Avg_BD_SSIM_csv',
		'BD_VMAF_db': 'Avg_BD_VMAF_db',
		'BD_VMAF_csv': 'Avg_BD_VMAF_csv'
	})

	# Reorder the columns to place the diff columns immediately after their corresponding metrics

	average_df = average_df[[
		'CODEC',
		'Avg_BD_PSNR_db', 'Avg_BD_PSNR_csv', 'BD_PSNR_diff',
		'Avg_BD_SSIM_db', 'Avg_BD_SSIM_csv', 'BD_SSIM_diff',
		'Avg_BD_VMAF_db', 'Avg_BD_VMAF_csv', 'BD_VMAF_diff'
	]]
	quality_threshold_exceeded = False
	if (average_df['BD_PSNR_diff'].abs() > 1).any() or \
	   (average_df['BD_SSIM_diff'].abs() > 1).any() or \
	   (average_df['BD_VMAF_diff'].abs() > 1).any():
	   logger.warning("QUALITY ALERT: Quality difference exceeds 1% threshold!")
	   logger.warning("This may indicate a regression in video quality.")
	   quality_threshold_exceeded = True

	# Print the new table with average values and differences
	logger.info("\nQuality Comparison Results:")
	logger.info(f"\n{average_df.to_string(index=False)}")

	return not quality_threshold_exceeded

def ensure_job_directory(base_dir, job_name):
    """Ensures the job directory exists, and if not, creates it or increments the name."""
    print_now("\n")
    original_job_name = job_name
    counter = 1
    while True:
        cmd = "mkdir -p" if "/" in job_name else "mkdir"
        output, exit_code = exec_command(f"cd {base_dir} && {cmd} {job_name}", print_cmd=False)
        if exit_code == 0:
            return job_name
        else:
            job_name = f"{original_job_name}_{counter}"
            counter += 1

def check_job_status(job_id):
	while True:
		cmd = f"squeue -j {job_id}"
		output, exit_code = exec_command(cmd, print_cmd=False)
		if exit_code == 0:
			if job_id not in output:
				print_now("Job has completed\n")
				break
		time.sleep(30)

def git_steps(job_dir: str, branch: str, url: str) -> bool:
	"""Execute git checkout with error handling"""
	cmd = f"git clone --depth 1 -b {branch} {url}"
	exit_code = exec_command(f"cd {job_dir} && {cmd}", print_cmd=False)
	if exit_code == 0:
		logger.info(f"Successfully checked out {branch} from {url.split('/')[-1]}")
		return True
	else:
		logger.error(f"Failed to checkout {branch} from {url.split('/')[-1]}")
		return False

def get_hash(repo_dir):
	cmd = f"git -C {repo_dir} rev-parse HEAD"
	output, exit_code = exec_command(cmd,print_cmd=False)
	return output

def running_cmds(job_dir,cmd):
	cmd = f"cd {job_dir} && " + cmd
	output, exit_code = exec_command(cmd)
	print(output)
	if exit_code != 0:
		print_now("Failed to execute commands")
		sys.exit(1)

def Build_and_compile_gaudi(Video_ip_fw: str) -> bool:
	"""Build and compile Gaudi with error handling"""
	logger.info("Compiling Gaudi...")
	output, exit_status = exec_command(f'cd {Video_ip_fw}/build && chmod 777 *.sh && ./cmake.sh {compile_flag}', print_cmd=False)
	if exit_status == 0:
		logger.info("Gaudi compilation completed successfully")
		return True
	else:
		logger.error(f"Gaudi compilation failed with exit code {exit_status}")
		return False

total_passes = count_passes()

if args.twopass:
	final_config = next(config for config in [config5, config4, config3, config2, config1] if config != "NONE.cfg")
else:
	final_config = config1
def report_generation():
	"""Main function to generate quality reports with comprehensive error handling"""
	log_jenkins_section("Video Quality Report Generation")
	logger.info(f"Host: {host_server}, User: {user}")
	logger.info(f"Total passes configured: {total_passes}")

	if total_passes == 0:
		logger.error("No config file selected - at least one config must be provided")
		sys.exit(ExitCodes.CONFIG_VALIDATION_FAILED.value)
	base_dir = "/mnt/ceph/Automation/quick_quality_test"
	job_name = ensure_job_directory(base_dir, args.job)
	job_dir = f"{base_dir}/{job_name}"
	print("job directory: ", job_dir)
	regression_quick_quality_scripts = "https://git.video:'r65j%4!oBUma'@git.netint.ca/quality_tests/regression_quick_quality_scripts.git"
	video_ip_ranger = "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/video_ip_ranger.git"
	regression_cfg = "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/regression_cfg.git"
	firmware = f"{job_dir}/regression_quick_quality_scripts"
	Video_ip_fw = f"{job_dir}/video_ip_ranger"
	try:
		log_jenkins_section("Repository Setup")
		logger.info("Checking out regression_quick_quality_scripts branch...")
		if not git_steps(job_dir, args.branch, regression_quick_quality_scripts):
			logger.error("Failed to checkout regression scripts")
			sys.exit(ExitCodes.JOB_EXECUTION_FAILED.value)

		if not args.custom_Gaudi:
			logger.info("Checking out video_ip branch and compiling Gaudi...")
			if not git_steps(job_dir, args.gaudibranch, video_ip_ranger):
				logger.error("Failed to checkout video_ip_ranger")
				sys.exit(ExitCodes.JOB_EXECUTION_FAILED.value)

			if not enable_or_disable_definations(Video_ip_fw):
				logger.warning("Some definition modifications failed")

			if not Build_and_compile_gaudi(Video_ip_fw):
				logger.error("Gaudi compilation failed")
				sys.exit(ExitCodes.JOB_EXECUTION_FAILED.value)

		logger.info("Checking out CFG branch...")
		if not git_steps(job_dir, args.configbranch, regression_cfg):
			logger.error("Failed to checkout regression_cfg")
			sys.exit(ExitCodes.JOB_EXECUTION_FAILED.value)

		log_jenkins_section("File Setup")
		if not copy_files_to_job(job_dir, Video_ip_fw, total_passes):
			logger.error("Failed to copy required files")
			sys.exit(ExitCodes.FILE_OPERATION_FAILED.value)
		print("\n")
		with open("logPathSanity.txt", "a") as f:
			for i in range(total_passes):
				config = globals()[f"config{i+1}"]
				job_pass_name = job_name if i == total_passes - 1 else os.path.join(job_name, f"pass{i+1}")
				job_pass_dir = os.path.join(base_dir, job_pass_name)
				upload_file(f"{config}",f"{job_pass_dir}/quality_cfg/{config}")
				if args.custom_Gaudi:
					upload_file(f"Gaudi",f"{job_pass_dir}/Gaudi")
					output, exit_status = exec_command(f"chmod +x {job_pass_dir}/Gaudi",print_cmd=False)
				cmd = f"python3 gaudi_quality.py -p {config} -j {job_pass_name} -t {job_name} --run_jobs"
				if args.twopass:
					cmd += " --encoding_only" if i != total_passes - 1 else " --create_detailed_report"
				else:
					cmd += " --create_detailed_report"
				print(cmd)
				f.write(f"\nUse Windows file explorer to open log files of jenkins at below link for pass{i+1}:\n\n")
				f.write('\\\\************\\video-quality-evaluation-testResult\\quick_quality_test\\{}\n'.format(job_pass_name))

				f.write("\nUse Linux file explorer to open log files of jenkins at below link:\n\n")
				f.write('//************/video-quality-evaluation-testResult/quick_quality_test/{}\n'.format(job_pass_name))

				exit_code = exec_command(f"cd {job_pass_dir} && {cmd}")

				if exit_code == 10:
					print_now(f"Config file validation failed")
					sys.exit(1)
				print(exit_code)
				#elif exit_code != 0:
				#	print_now(f"Failed to execute commands {cmd}")
				#	sys.exit(1)
		cmake_file = f"{Video_ip_fw}/CMakeLists.txt"
		final_config_path = f"{job_name}/quality_cfg/{final_config}"
		withTAV = check_tav(final_config_path)
		remote_file_path = f"{job_dir}/data.csv"
		local_directory = os.getcwd()
		download_file(remote_file_path,local_directory)
		today_date = datetime.date.today().isoformat()
		videoip_tag = args.gaudibranch
		videoip_hash = get_hash(Video_ip_fw)
		cfg_branch = args.configbranch
		cfgbranch_hash = get_hash(f"{job_dir}/regression_cfg")
		cfg_key = 'config1' if not args.twopass else f"config{total_passes}"
		cfg_file = globals().get(cfg_key, None)
		excel_sheet = f"{job_dir}/cnm_wave677_{cfg_file.split('.')[0]}_self.xlsx"
		download_file(excel_sheet,local_directory)
		test_type = "1Pass" if not args.twopass else "2Pass"
		metadata = {
			'DATE': today_date,
			'videoip_branch': videoip_tag,
			'Videoip_hash': videoip_hash,
			'cfg_branch': cfg_branch,
			'cfg_files_hash': cfgbranch_hash,
			'cfg_file_name': cfg_file,
			'test_type': test_type,
			'withTAV': withTAV
		}
		core_file = f"{local_directory}/data.csv"
		generate_csv(core_file,metadata)
		if args.save_to_db:
			log_jenkins_section("Database Storage")
			try:
				store_to_db(f"{local_directory}/result.csv")
				logger.info("Results stored to database successfully")
			except Exception as e:
				logger.error(f"Failed to store results to database: {e}")
				sys.exit(ExitCodes.DATABASE_ERROR.value)

		log_jenkins_section("Quality Analysis")
		quality_ok = compare_bdrate(withTAV, args.Compare_with, args.config_file)
		if not quality_ok:
			logger.error("Quality threshold exceeded - failing build")
			sys.exit(ExitCodes.QUALITY_THRESHOLD_EXCEEDED.value)
	finally:
		log_jenkins_section("Cleanup and Archival")
		try:
			os.makedirs("archive", exist_ok=True)
			files_to_archive = [
				"data.csv",
				"logPathSanity.txt",
				"jenkins_log.txt"
			]

			cfg_key = 'config1' if not args.twopass else f"config{total_passes}"
			cfg_file = globals().get(cfg_key, None)
			if cfg_file:
				files_to_archive.append(f"cnm_wave677_{cfg_file.split('.')[0]}_self.xlsx")

			for file in files_to_archive:
				if os.path.exists(file):
					getoutput(f"mv {file} archive/")
					logger.info(f"Archived: {file}")

			logger.info("Cleanup completed successfully")
		except Exception as e:
			logger.warning(f"Cleanup failed: {e}")

def print_execution_summary():
	"""Print a comprehensive execution summary for Jenkins"""
	log_jenkins_section("Execution Summary")

	logger.info(f"Job Name: {args.job}")
	logger.info(f"Branch: {args.branch}")
	logger.info(f"Gaudi Branch: {args.gaudibranch}")
	logger.info(f"Config Branch: {args.configbranch}")
	logger.info(f"Total Passes: {total_passes}")
	logger.info(f"Two Pass Mode: {'Enabled' if args.twopass else 'Disabled'}")
	logger.info(f"Custom Gaudi: {'Yes' if args.custom_Gaudi else 'No'}")
	logger.info(f"Save to DB: {'Yes' if args.save_to_db else 'No'}")

	if enable_defination:
		logger.info(f"Enabled Definitions: {enable_defination}")
	if disable_defination:
		logger.info(f"Disabled Definitions: {disable_defination}")

	# Check if archive directory exists and list contents
	if os.path.exists("archive"):
		archived_files = os.listdir("archive")
		logger.info(f"Archived Files: {', '.join(archived_files)}")

	logger.info("All operations completed successfully!")

def main():
	"""Main entry point with comprehensive error handling for Jenkins"""
	try:
		log_jenkins_section("Starting Video Quality Evaluation")
		logger.info(f"Script arguments: {' '.join(sys.argv[1:])}")

		# Validate environment
		if not validate_environment():
			logger.error("Environment validation failed")
			sys.exit(ExitCodes.GENERAL_ERROR.value)

		# Run main process
		report_generation()

		# Print execution summary
		print_execution_summary()

		log_jenkins_section("Video Quality Evaluation Completed Successfully")
		logger.info("All operations completed successfully")
		sys.exit(ExitCodes.SUCCESS.value)

	except KeyboardInterrupt:
		logger.error("Process interrupted by user")
		sys.exit(ExitCodes.GENERAL_ERROR.value)
	except SystemExit as e:
		# Re-raise SystemExit to preserve exit codes
		raise
	except Exception as e:
		logger.error(f"Unexpected error occurred: {e}")
		logger.error("Stack trace:", exc_info=True)
		sys.exit(ExitCodes.GENERAL_ERROR.value)

def validate_environment() -> bool:
	"""Validate environment and dependencies"""
	try:
		# Check if required directories exist (only local validation)
		base_dir = "/mnt/ceph/Automation/quick_quality_test"
		logger.info("Validating local environment...")

		# Validate credentials are loaded
		if not all([host_server, user, password]):
			logger.error("Missing required credentials (host_server, user, password)")
			return False

		logger.info(f"Using host: {host_server}, user: {user}")

		# Check if config files exist
		required_configs = [f"config{i}" for i in range(1, 6)]
		valid_configs = []
		for config_var in required_configs:
			config_value = globals().get(config_var)
			if config_value and config_value != "NONE.cfg":
				valid_configs.append(config_var)

		if not valid_configs:
			logger.error("No valid config files specified")
			return False

		logger.info(f"Valid configs found: {', '.join(valid_configs)}")
		logger.info("Environment validation passed")
		return True

	except Exception as e:
		logger.error(f"Environment validation failed: {e}")
		return False

if __name__ == "__main__":
	main()