#!/usr/bin/python3
"""
Debug script to help identify issues with generate_summary_worksheet_per_sequence
This script can be used to test the Excel generation with sample data
"""

import sys
import os

# Add the current directory to Python path to import gaudi_quality_lib
sys.path.append('.')

try:
    import gaudi_quality_lib
    import xlsxwriter
    import numpy as np
    print("Successfully imported required modules")
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the correct directory")
    sys.exit(1)

def create_sample_data():
    """Create sample simulation_results data for testing"""
    simulation_results = {
        'cnm_wave677': {
            'h264': {
                'h264_BasketballDrill_832x480p50': {
                    'psnr_y': -5.2, 'psnr_u': -3.1, 'psnr_v': -2.8,
                    'ssim_y': -4.1, 'ssim_u': -2.9, 'ssim_v': -2.5,
                    'vmaf': -6.3, 'apsnr': -4.8, 'assim': -3.7
                },
                'h264_BQMall_832x480p60': {
                    'psnr_y': -4.8, 'psnr_u': -2.9, 'psnr_v': -2.6,
                    'ssim_y': -3.9, 'ssim_u': -2.7, 'ssim_v': -2.3,
                    'vmaf': -5.9, 'apsnr': -4.4, 'assim': -3.5
                }
            },
            'h265': {
                'h265_BasketballDrill_832x480p50': {
                    'psnr_y': -3.2, 'psnr_u': -2.1, 'psnr_v': -1.8,
                    'ssim_y': -2.9, 'ssim_u': -1.9, 'ssim_v': -1.5,
                    'vmaf': -4.1, 'apsnr': -2.8, 'assim': -2.4
                },
                'h265_BQMall_832x480p60': {
                    'psnr_y': -2.9, 'psnr_u': -1.8, 'psnr_v': -1.6,
                    'ssim_y': -2.6, 'ssim_u': -1.7, 'ssim_v': -1.3,
                    'vmaf': -3.8, 'apsnr': -2.5, 'assim': -2.1
                }
            }
        }
    }
    return simulation_results

def test_excel_generation():
    """Test the generate_summary_worksheet_per_sequence function"""
    print("Creating test data...")
    simulation_results = create_sample_data()
    
    # Sample input list (as it would come from config file)
    input_list = [
        'BasketballDrill_832x480p50_8bit',
        'BQMall_832x480p60_8bit'
    ]
    
    # Create a test workbook
    workbook = xlsxwriter.Workbook('test_debug_output.xlsx')
    
    # Add formats
    format1 = workbook.add_format({'bg_color': '#FFC7CE', 'font_color': '#9C0006'})
    format2 = workbook.add_format({'bg_color': '#C6EFCE', 'font_color': '#006100'})
    number_format = workbook.add_format({'num_format': '0.00'})
    
    format_positive = {'type': 'cell', 'criteria': 'greater than', 'value': 0, 'format': format1}
    format_negative = {'type': 'cell', 'criteria': 'less than or equal to', 'value': 0, 'format': format2}
    
    # Test H.264 worksheet
    print("\\nTesting H.264 worksheet...")
    worksheet_h264 = workbook.add_worksheet('Sequences H.264')
    gaudi_quality_lib.generate_summary_worksheet_per_sequence(
        simulation_results, worksheet_h264, format_positive, format_negative, number_format,
        'h264', 'cnm_wave677', ['cnm_wave677'], 'medium', 'test.cfg', input_list
    )
    
    # Test H.265 worksheet
    print("\\nTesting H.265 worksheet...")
    worksheet_h265 = workbook.add_worksheet('Sequences H.265')
    gaudi_quality_lib.generate_summary_worksheet_per_sequence(
        simulation_results, worksheet_h265, format_positive, format_negative, number_format,
        'h265', 'cnm_wave677', ['cnm_wave677'], 'medium', 'test.cfg', input_list
    )
    
    workbook.close()
    print("\\nTest completed. Check 'test_debug_output.xlsx' for results.")
    print("If the worksheets are no longer empty, the fix is working!")

if __name__ == "__main__":
    test_excel_generation()
