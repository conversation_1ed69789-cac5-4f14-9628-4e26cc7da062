#!/usr/bin/python3
import os, argparse, re, shutil, time,  getpass, ast, sys, math
from subprocess import Popen, getoutput, getstatusoutput, PIPE, STDOUT, check_output, run
import configparser
import gaudi_quality_lib
from itertools import product

user = getpass.getuser()
current_directory = os.getcwd()
############### Basic Parameters Map ###############
codec_map = {'0': 'h264', '1': 'h265', '2': 'av1'}
simulation_type_map = {'0': 'cbr', '1': 'cqp', '2': 'crf'}
mode_map = {'0': 'bframes', '1': 'ippp'}
anchor_codec_map = {'h264': 'avc', 'h265': 'hevc', 'av1': 'av1'}
video_path_map = {'1920x1080': 'quadra_rc_1080p', '960x544': 'cce_scaler',
                  '1280x720': 'UGC_720p_yuv',
                  '3840x2160': {'p30': '4Kp30', 'p60': '4Kp60'}}
max_instance_map = {'3840x2160': 3, '1920x1080': 7, '1280x720': 9}
vmaf_map = {'1920x1080': 'vmaf_v0.6.1.json', '960x544': 'vmaf_v0.6.1.json',
            '1280x720': 'vmaf_v0.6.1.json',
            '3840x2160': 'vmaf_4k_v0.6.1.json'}
vidip_ffmpeg = '/home/<USER>/inputs/utils/VIDIP/ffmpeg'.format(user)

############### Grab the Type of CFG ###############
parser = argparse.ArgumentParser(formatter_class=argparse.RawTextHelpFormatter)
parser.add_argument("-p", type=str,nargs='+',
                    help="Select quality dataset type\n\n")
parser.add_argument('-j',
                    '--job',
                    type=str,
                    default=current_directory,
                    help='Provide the job name\n\n')
parser.add_argument('-t',
                    '--root_dir',
                    type=str,
                    default=current_directory,
                    help='Provide the root directory name\n\n')
parser.add_argument('-r',
                    '--project',
                    type=str,
                    default="Panorama-cmodel",
                    help='Provide project name\n\n')
parser.add_argument('-s',
                    '--sourcedir',
                    type=str,
                    default="/mnt/ceph/sources/",
                    help='Provide sources directory\n\n')
parser.add_argument('-v',
                    '--version',
                    type=str,
                    default="2.4",
                    help='Provide Anchors version, Usage: general or 2.4 \n\n')
parser.add_argument("--cleanup", action="store_true", help="Removes all temporary and intermediate files")
parser.add_argument("--encoding_only", action="store_true", help="Run only the encoding job")
parser.add_argument("--generate_scripts", action="store_true", default=True, help="generate scripts only")
parser.add_argument("--create_detailed_report", action="store_true", help="Generate detailed")
parser.add_argument("--run_jobs", action="store_true", help="run_encoding_and_decoding")
args = parser.parse_args()

if not args.cleanup and not args.p:
    parser.error("the following arguments are required: -p (unless --cleanup is specified)")

def print_now(text):
	with open('scriptlog.txt','a') as outputFile:
		outputFile.write(time.strftime("%Y-%m-%d %H:%M:%S") + ' '*7  + str(text) + '\n')
	print('{}'.format(text))
	sys.stdout.flush()

def clean_up():
    items_to_remove = ["all.sh", "all_analysis.sh", "av1", "h264", "h265", "gaudi_cfg", "SatdFile", "dqp_files", "avg_dqp_files", "MCTF_YUV"]
    for item in items_to_remove:
        if os.path.exists(item):
            try:
                run(["rm", "-rf", item], check=True)
            except Exception as e:
                print(f"Error removing {item}: {e}")
    print("\nCleanup completed successfully")
if args.cleanup:
    clean_up()
    sys.exit(0)

cfg_file_names = args.p
configFiles = [cfg_file_name for cfg_file_name in cfg_file_names[0].split(',')]

############### Prepare Environment for Panorama C-model ##############



if args.project== "Quadra":
    video_dir = '/home/<USER>/sources/'.format(user)
    test_root = '/home/<USER>/XCODER/'.format(user)
else:
    video_dir = args.sourcedir
    job_name = args.job
    test_root = current_directory if args.job == current_directory else f"/mnt/ceph/Automation/quick_quality_test/{args.job}" 
    root_dir = current_directory if args.root_dir == current_directory else f"/mnt/ceph/Automation/quick_quality_test/{args.root_dir}"



################### Submit Jobs ##################

def submit_job(job_dir, cmd):
    try:
        # Use subprocess.check_output to run the command and capture the output
        output = check_output(cmd, shell=True, cwd=job_dir, text=True)
        
        # Search for the job ID in the output
        match = re.search(r'\b\d+\b', output)
        if match:
            return match.group()
        else:
            print_now("Job_id not found")
            return None
    except subprocess.CalledProcessError as e:
        # Handle errors in the command execution
        print_now(f"Command failed with exit code {e.returncode}: {e.output}")
        return None
    except Exception as e:
        # Handle other potential errors
        print_now(f"An error occurred: {e}")
        return None

#################### Check job Status ##################

def check_job_status(job_id, poll_interval=30, max_attempts=4000):
    attempts = 0
    
    while attempts < max_attempts:
        try:
            # Run the command and capture the output
            cmd = f"squeue -j {job_id}"
            result = run(
                cmd,
                shell=True,          # Use shell=True to run the command in the shell
                capture_output=True, # Capture stdout and stderr
                text=True,           # Return output as a string (not bytes)
                check=True           # Raise an exception for non-zero exit codes
            )
            
            # Get the command output
            output = result.stdout
            sys.stdout.flush()

            # Check if the job_id is still present in the output
            lines = output.strip().split('\n')
            job_found = any(job_id in line for line in lines[1:])  # Skip the header line
            
            if not job_found:
                print("Job has completed")
                break
        except subprocess.CalledProcessError as e:
            # Handle errors in the command execution
            print(f"Command failed with exit code {e.returncode}: {e.stderr}")
            break
        except Exception as e:
            # Handle other potential errors
            print(f"An error occurred: {e}")
            break
        
        # Wait for the defined interval before checking again
        attempts += 1
        time.sleep(poll_interval)
    
    if attempts >= max_attempts:
        print("Maximum polling attempts reached. Job status is unknown.")
        sys.exit(1)

def execute_script_in_directory(script, directory):
    try:
        # Change the current working directory
        os.chdir(directory)
        
        # Start the process
        process = Popen(
            ["python3", script],
            stdout=PIPE,
            stderr=PIPE
        )
        
        # Wait for the process to complete and get the output and error
        stdout, stderr = process.communicate()
        
        # Check the return code
        if process.returncode == 0:
            print("Retried failed jobs if any")
            return True
        else:
            print(f"Command failed with return code {process.returncode}.")
            print(stderr.decode())
            return False

    except FileNotFoundError as e:
        print(f"Error: {e}")
        return False
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return False

for cfg_file_name in configFiles:
    print_now(f"\nGenerating scripts for encoding and decoding {cfg_file_name}\n")
        
    ############### Parse CFG File ###############
    config_file = f'quality_cfg/{cfg_file_name}'
    if not gaudi_quality_lib.validate_config(config_file):
        print_now('config validation Failed (FAILURE)')
        sys.exit(10)
    codecs, crf, input_list, bitrate_list, anchor_list, crf_list, params_dict, simulation_type, mode, encoder_codec, mctf_inputs, mctf_inputs_cqp = gaudi_quality_lib.get_config_value(config_file)
    simulation_type = simulation_type_map[simulation_type]
    mode = mode_map[mode]
    ############### Prepare commands ###############
    CMDs = []
    quality_CMDs = []
    sub_cmds = []
    sub_quality_cmds = []
    sanity_check_evaluated_points = len(bitrate_list)
    analysis_group = {}
    analysis_group[encoder_codec] = {}
    for codec in codecs:
        if not os.path.isdir('{}/{}'.format(test_root,codec_map[codec])):
            getoutput('mkdir {}/{}'.format(test_root,codec_map[codec]))
        analysis_group[encoder_codec][codec_map[codec]] = []
    gaudi_quality_lib.gaudi_command_prepare(test_root, root_dir,codecs, input_list, bitrate_list,  simulation_type, cfg_file_name, mode, params_dict, video_path_map, codec_map, vmaf_map, analysis_group, encoder_codec, video_dir, mctf_inputs, mctf_inputs_cqp)

    ############### Trigger commands ###############
    if args.run_jobs:
        encoding_job_id = submit_job(f'{test_root}','sbatch all.sh')
        getoutput(f"sudo scontrol top {encoding_job_id}")
        if encoding_job_id:
            print_now(f"Running encoding job with jobid {encoding_job_id}")
        else:
            raise Exception("Encoding job failed")
        check_job_status(encoding_job_id)#
        execute_script_in_directory("retry_failed_slurm_jobs.py", test_root)
        if not args.encoding_only:
            decoding_job_id = submit_job(f'{test_root}','sbatch all_analysis.sh')
            getoutput(f"sudo scontrol top {decoding_job_id}")
            if decoding_job_id:
                print_now(f"Running decoding job with jobid {decoding_job_id}")
            else:
                raise Exception("Decoding job failed")
            check_job_status(decoding_job_id)
            time.sleep(120)

    ############### Analysis the quality .txt file ###############
    if args.create_detailed_report:
        for group in analysis_group:
            for codec in analysis_group[group]:
                summary_file = '{0}/{1}/{1}_analysis.txt'.format(test_root, codec)
                summary_file_path = open(summary_file, "w", 1)
                summary_file_path.write(
                    '-' * 5 + 'Output File Name' + '-' * 5 + 'Bitrate' + '-' * 5 + 'PSNR_y' + '-' * 5 + 'PSNR_u' + '-' * 5 + 'PSNR_v'
                    + '-' * 5 + 'SSIM_y' + '-' * 5 + 'SSIM_u' + '-' * 5 + 'SSIM_v' + '-' * 5 + 'VMAF' + '-' * 5 + 'Frame' + '-' * 5 + '\n')
                for item in analysis_group[group][codec]:
                    gaudi_quality_lib.parse_info(summary_file_path, test_root, *item)

        for anchor in anchor_list:
            simulation_result = {}
            simulation_graph = {}
            ############### Compared the quality .txt with Anchor ###############
            for encoder in analysis_group:
                for group in analysis_group[encoder]:
                    summary_file = '{0}/{1}/{1}_analysis.txt'.format(test_root, group)
                    n = sanity_check_evaluated_points
                    sel_range_low = list(range(min(4, n)))
                    sel_range_high = list(range(max(0, n-4), n))
                    if n >= 6:
                        start = math.ceil(n/2) - 2
                        sel_range_medium = list(range(start, start+4))
                    else:
                        sel_range_medium = sel_range_high
                    sel_range_all = list(range(n))
                    main_group = encoder
                    if main_group not in simulation_result:
                        simulation_result[main_group] = {}
                        simulation_graph[main_group]= {}
                    if group not in simulation_result[main_group]:
                        simulation_result[main_group][group] = {}
                        simulation_graph[main_group][group] = {}
                    for video in input_list:
                        sequence = '_'.join(video.split('_')[:-1])
                        input_data = gaudi_quality_lib.read_data(summary_file, sequence, sanity_check_evaluated_points)
                        ref_codec = group.split('_')[-1]
                        crf_path = 'CRF_ANCHOR/' if 'crf1' in group else ''
                        ref_path = 'ANCHORS/PanoBringUp_{}_1080p60_{}/{}/{}_{}/{}/analysis/summary.txt'.format(simulation_type,mode,args.version,
                                                                                ref_codec, anchor,
                                                                                anchor_codec_map[ref_codec])
                        reference_data = gaudi_quality_lib.read_data(ref_path, sequence, sanity_check_evaluated_points)
                        sim_name = group + '_' + sequence
                        bd_res_low = gaudi_quality_lib.bd_rate_compute(input_data, reference_data, sel_range_low, sanity_check_evaluated_points)
                        bd_res_medium = gaudi_quality_lib.bd_rate_compute(input_data, reference_data, sel_range_medium, sanity_check_evaluated_points)
                        bd_res_high = gaudi_quality_lib.bd_rate_compute(input_data, reference_data, sel_range_high, sanity_check_evaluated_points)
                        bd_res_all = gaudi_quality_lib.bd_rate_compute(input_data, reference_data, sel_range_all, sanity_check_evaluated_points)

                        simulation_r = simulation_result[main_group]
                        simulation_g = simulation_graph[main_group]
                        gaudi_quality_lib.add_entry(simulation_r, group, sim_name + '_low', bd_res_low)
                        gaudi_quality_lib.add_entry(simulation_r, group, sim_name + '_medium', bd_res_medium)
                        gaudi_quality_lib.add_entry(simulation_r, group, sim_name + '_high', bd_res_high)
                        gaudi_quality_lib.add_entry(simulation_r, group, sim_name + '_all', bd_res_all)
                        gaudi_quality_lib.add_entry_raw_data(simulation_g, group, sim_name, input_data, reference_data)
            ############### Generate Excel Summary ###############
            for main_group in simulation_result.keys():
                reported_codec = [codec_map[codec] for codec in codecs]
                if 'svt_av1_preset' in anchor:
                    if 'av1' in reported_codec:
                        reported_codec = ['av1']
                    else:
                        continue
                print_now('-' * 15 + 'Generating {}_{}_{} Excel Summary'.format(main_group, cfg_file_name.split('.')[0],
                                                                            anchor) + '-' * 15)
                encoder = [encoder_codec] #Keep consistent with quadra, could improve later
                cqp_list = [] if simulation_type != 'cqp' else bitrate_list
                gaudi_quality_lib.generate_detailed_report_view(simulation_result, simulation_graph, main_group,
                                                                cfg_file_name, anchor, reported_codec,
                                                                encoder,
                                                                bitrate_list, cqp_list, input_list,
                                                                sanity_check_evaluated_points)