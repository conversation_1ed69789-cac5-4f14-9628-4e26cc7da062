# RD Curves Fix Summary

## Problem Identified
The RD curve sheets were empty because the functions were trying to read data from detailed worksheets instead of using the raw measurement data from `simulation_graphs`.

## Root Cause Analysis

### **Original Flawed Approach:**
1. **Wrong Data Source:** RD curve functions tried to read from detailed worksheets
2. **Wrong Data Type:** Attempted to use BD-rate summary values instead of raw measurement arrays
3. **Missing Parameters:** Functions didn't have access to `simulation_graphs` data
4. **Incorrect Column Mapping:** Hardcoded column indices that didn't match actual worksheet structure

### **What RD Curves Actually Need:**
- **Raw measurement arrays** (bitrate, PSNR, SSIM, VMAF for each QP/CRF setting)
- **Point-by-point data** to create scatter plots
- **Both test and reference encoder data** for comparison
- **Direct access to `simulation_graphs`** data structure

## Fixes Implemented

### **1. Corrected Data Source**
**Before:**
```python
# Tried to read from detailed worksheets with hardcoded column indices
s_col_ref_data_br = 3
s_col_ref_data_pnsr = 4
x = [s_row_data, s_col_ref_data_br, s_row_data + row_increment - 1, s_col_ref_data_br]
y = [s_row_data, s_col_ref_data_pnsr, s_row_data + row_increment - 1, s_col_ref_data_pnsr]
```

**After:**
```python
# Read directly from simulation_graphs raw data
br_seq = simulation_graphs[main_group][seq_key]['br_seq']
psnr_y_seq = simulation_graphs[main_group][seq_key]['psnr_y_seq']
br_ref = simulation_graphs[main_group][seq_key]['br_ref']
psnr_y_ref = simulation_graphs[main_group][seq_key]['psnr_y_ref']
```

### **2. Enhanced Function Signatures**
**Before:**
```python
def generate_graph_worksheet(workbook, worksheet, worksheet_list, ref_type, rdo, reference_name, row_index,
                             input_list, sanity_check_evaluated_points):
```

**After:**
```python
def generate_graph_worksheet(workbook, worksheet, worksheet_list, ref_type, rdo, reference_name, row_index,
                             input_list, sanity_check_evaluated_points, simulation_graphs, main_group):
```

### **3. Proper Data Writing and Charting**
**New Approach:**
1. **Write raw data to worksheet** for each sequence
2. **Create chart series** directly from written data
3. **Use proper Excel chart API** instead of custom functions
4. **Handle missing data gracefully** with debug output

### **4. Metric-Specific Data Access**
- **PSNR:** Uses `psnr_y_seq` and `psnr_y_ref`
- **VMAF:** Uses `vmaf_seq` and `vmaf_ref`
- **SSIM:** Uses `ssim_y_seq` and `ssim_y_ref`

## Technical Implementation

### **Data Structure Used:**
```python
simulation_graphs[main_group][sequence_key] = {
    'br_seq': [bitrate_array_test],      # Test encoder bitrates
    'psnr_y_seq': [psnr_array_test],     # Test encoder PSNR values
    'vmaf_seq': [vmaf_array_test],       # Test encoder VMAF values
    'ssim_y_seq': [ssim_array_test],     # Test encoder SSIM values
    'br_ref': [bitrate_array_ref],       # Reference encoder bitrates
    'psnr_y_ref': [psnr_array_ref],      # Reference encoder PSNR values
    'vmaf_ref': [vmaf_array_ref],        # Reference encoder VMAF values
    'ssim_y_ref': [ssim_array_ref]       # Reference encoder SSIM values
}
```

### **Chart Creation Process:**
1. **Find sequence data** using multiple possible key formats
2. **Write raw data points** to worksheet columns
3. **Create chart** with appropriate axis labels
4. **Add reference series** (red circles)
5. **Add test encoder series** (blue squares)
6. **Position chart** in worksheet

### **Enhanced Debug Output:**
- Shows available sequences in `simulation_graphs`
- Indicates which sequences are found/missing
- Reports successful data loading
- Warns about missing raw data

## Expected Results

### **Before Fix:**
- Empty RD curve worksheets
- No charts generated
- Silent failures

### **After Fix:**
- **Populated RD curve worksheets** with raw data
- **Visual scatter plot charts** showing rate-distortion curves
- **Multiple metric support:** PSNR, VMAF, SSIM
- **Proper comparison:** Test encoder vs reference encoder
- **Debug output** showing data processing status

## Chart Features

### **Visual Elements:**
- **X-axis:** Bitrate (Kbps)
- **Y-axis:** Quality metric (PSNR dB, VMAF Score, SSIM Score)
- **Reference data:** Red circles with trend line
- **Test encoder data:** Blue squares with trend line
- **Multiple sequences:** Each sequence gets its own chart

### **Chart Types:**
- **RD Curve H264/H265/AV1 PSNR:** Rate vs PSNR analysis
- **RD Curve H264/H265/AV1 VMAF:** Rate vs perceptual quality analysis
- **RD Curve H264/H265/AV1 SSIM:** Rate vs structural similarity analysis

## Usage

### **When to Use Each RD Curve:**
1. **PSNR RD Curves:** Traditional quality analysis, technical evaluation
2. **VMAF RD Curves:** Perceptual quality analysis, user experience optimization
3. **SSIM RD Curves:** Structural quality analysis, detail preservation assessment

### **How to Interpret:**
- **Lower and to the right is better** (lower bitrate for same quality)
- **Steeper curves** indicate better compression efficiency
- **Compare test vs reference** to see performance differences

## Files Modified
- **`gaudi_quality_lib.py`** - Fixed all RD curve generation functions

## Next Steps
1. **Run your scripts** and check for debug output
2. **Verify RD curve sheets** contain charts and data
3. **Check console output** for any remaining data issues
4. **Remove debug print statements** once everything works correctly

The RD curves should now properly display the rate-distortion performance of your encoders across different quality metrics!
